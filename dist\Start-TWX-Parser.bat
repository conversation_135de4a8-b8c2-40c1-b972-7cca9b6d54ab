@echo off
echo ========================================
echo    TWX Parser - Standalone Tool
echo ========================================
echo.
echo Starting TWX Parser Web Interface...
echo.
echo * The web browser will open automatically
echo * If it doesn't open, manually go to the URL shown below
echo * Use Ctrl+C to stop the server when done
echo.
echo Starting server...
echo.

twx-parser.exe --ui

echo.
echo TWX Parser has stopped.
echo Thank you for using TWX Parser!
pause
