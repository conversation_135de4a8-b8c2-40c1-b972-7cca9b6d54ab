{"name": "TWX Parser", "version": "1.7.0", "description": "Standalone TWX Parser for IBM BPM", "buildDate": "2025-06-29", "platform": "Windows x64", "nodeVersion": "18.x", "features": ["Web Interface with File Upload", "Collapsible Object Browser", "Deep Search with Highlighting", "Command Line Interface", "JSON Output Generation", "Multiple Object Type Support"], "supportedObjectTypes": ["CSHS", "Process", "Coach <PERSON>", "Business Object", "Environment Variables", "Participants", "Resource Bundles", "ESArtifacts", "Managed Assets"], "files": {"twx-parser.exe": "Main executable file", "README.md": "Complete documentation", "Example.twx": "Sample TWX file for testing", "Start-TWX-Parser.bat": "Quick start web interface", "Example-CLI-Parse.bat": "Command line example"}, "systemRequirements": {"os": "Windows 10/11 x64", "ram": "4GB minimum, 8GB recommended", "diskSpace": "500MB free space", "browser": "Any modern web browser"}}