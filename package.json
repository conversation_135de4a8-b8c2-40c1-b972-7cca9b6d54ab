{"name": "twx-parser", "version": "1.7.0", "description": "Library for parsing and querying TWX files for IBM BPM", "main": "app.js", "bin": {"twx-parser": "./app.js"}, "scripts": {"start": "node app.js", "viewer": "node start-viewer.js", "test": "echo \"No tests specified\" && exit 0", "lint": "eslint .", "type-check": "tsc --noEmit", "check": "npm run lint && npm run type-check", "build": "pkg . --out-path dist --targets node18-win-x64"}, "repository": {"type": "git", "url": "https://github.com/tigermarques/twx-parse.git"}, "keywords": ["IBM", "BPM", "TWX", "parser"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tigermarques/twx-parse/issues"}, "homepage": "https://github.com/tigermarques/twx-parse#readme", "dependencies": {"adm-zip": "^0.5.10", "fast-xml-parser": "^4.2.5", "node-fetch": "^2.7.0", "prismjs": "^1.30.0", "xml2js": "^0.4.23"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.35.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-n": "^17.21.3", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-regexp": "^2.10.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.5", "eslint-plugin-unicorn": "^55.0.0", "pkg": "^5.8.1", "prettier": "^3.6.2", "typescript": "^5.9.2"}, "engines": {"node": ">=14.0.0"}, "pkg": {"assets": ["twx-viewer-new.html", "twx-viewer-new.css", "twx-viewer-new.js", "output/*.json"]}}