/* TWX Parser - New UI Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

/* Panel Controls */
.panel-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Toolkit Toggle */
.toolkit-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.toolkit-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 40px;
    height: 20px;
    background-color: #ccc;
    border-radius: 20px;
    transition: background-color 0.3s;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
}

.toolkit-toggle input[type="checkbox"]:checked + .toggle-slider {
    background-color: #007bff;
}

.toolkit-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-weight: 500;
    color: #495057;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

/* File Selection Styles */
.file-selection {
    margin: 1rem 2rem;
}

.file-input-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.select-file-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.select-file-btn:hover {
    background: #218838;
}

.selected-file-info {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.parse-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.parse-btn:hover:not(:disabled) {
    background: #0056b3;
}

.parse-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.clear-file-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.clear-file-btn:hover {
    background: #c82333;
}

.parsing-status {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.status-message {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.status-success {
    color: #155724;
    background: #d4edda;
    border-color: #c3e6cb;
}

.status-error {
    color: #721c24;
    background: #f8d7da;
    border-color: #f5c6cb;
}

.status-processing {
    color: #004085;
    background: #cce7ff;
    border-color: #b3d7ff;
}

/* Collapsible Panel Styles */
.collapsible-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.collapsible-panel:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.panel-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.panel-header:hover {
    background: #e9ecef;
}

.panel-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.panel-toggle {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    color: #6c757d;
}

.panel-toggle.collapsed {
    transform: rotate(-90deg);
}

.panel-content {
    padding: 1.5rem;
    display: block;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.panel-content.collapsed {
    max-height: 0;
    padding: 0 1.5rem;
}

/* Object Browser Styles */
.object-browser {
    margin-bottom: 2rem;
}

.loading {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

.object-types-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
}

.object-type-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 60px;
    display: flex;
    align-items: center;
}

.object-type-card:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-2px);
}

.object-type-card.selected {
    background: #d1ecf1;
    border-color: #bee5eb;
}

.object-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.object-type-name {
    font-weight: bold;
    flex-grow: 1;
}

.object-count-badge {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin-left: 8px;
}

/* Objects List Styles */
.objects-list {
    max-height: 400px;
    overflow-y: auto;
}

/* Object List Container */
.object-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 0.75rem;
    padding: 0.5rem;
}

.object-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    min-height: 80px;
}

.object-item:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.object-item.selected {
    background: #d1ecf1;
    border-color: #bee5eb;
}

.object-item.toolkit {
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.object-item.application {
    border-left: 4px solid #28a745;
}

.object-name {
    font-weight: bold;
    flex-grow: 1;
}

.toolkit-details {
    font-size: 0.9em;
    color: #666;
    margin-top: 4px;
    padding: 4px 8px;
    background: #e7f3ff;
    border-radius: 4px;
}

/* Object Details Styles */
.object-details {
    max-height: 500px;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 1.5rem;
}

.detail-section-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    border: 1px solid #dee2e6;
}

.detail-section-header:hover {
    background: #e9ecef;
}

.detail-section-title {
    font-weight: 600;
    color: #495057;
}

.detail-content {
    padding: 1rem;
    background: #fdfdfe;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-top: 0.5rem;
}

.detail-content.collapsed {
    display: none;
}

/* Deep Search Styles */
.deep-search {
    margin-top: 2rem;
}

.search-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

#search-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

#search-input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

#search-button, #clear-search-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#search-button {
    background: #007bff;
    color: white;
}

#search-button:hover {
    background: #0056b3;
}

#clear-search-button {
    background: #6c757d;
    color: white;
}

#clear-search-button:hover {
    background: #545b62;
}

.search-info {
    background: #d1ecf1;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    color: #0c5460;
}

.search-results-list {
    max-height: 400px;
    overflow-y: auto;
}

.search-result-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-result-item:hover {
    border-color: #adb5bd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.search-result-title {
    font-weight: 600;
    color: #495057;
}

.search-result-type {
    background: #6f42c1;
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.search-result-preview {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    max-height: 150px;
    overflow-y: auto;
}

.highlight {
    background: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 2px;
    font-weight: 600;
}

/* Status Bar */
.status-bar {
    background: #f8f9fa;
    padding: 0.75rem 2rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #6c757d;
}

/* Code/Script Display */
.code-block {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    white-space: pre-wrap;
}

/* Variables Table */
.variables-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem;
}

.variables-table th,
.variables-table td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    text-align: left;
}

.variables-table th {
    background: #f8f9fa;
    font-weight: 600;
}

/* Variable Boxes Layout */
.variables-boxes {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 0.5rem;
}

.variable-type-section {
    margin-bottom: 1rem;
}

.variable-type-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 1rem;
}

.variable-boxes-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.variable-box {
    display: inline-flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    font-size: 0.85rem;
    position: relative;
    min-width: 80px;
    max-width: 200px;
}

.variable-box .variable-name {
    margin: 0 0.3rem;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.variable-box .default-indicator {
    width: 12px;
    height: 12px;
    border: 1px solid #6c757d;
    border-radius: 2px;
    margin-right: 0.3rem;
    background: transparent;
    flex-shrink: 0;
}

.variable-box .default-indicator.has-default {
    background: #28a745;
    border-color: #28a745;
}

.variable-box .default-indicator.has-default::after {
    content: '✓';
    color: white;
    font-size: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* Script Section Styling */
.script-section {
    margin-top: 1rem;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
}

.script-section h5 {
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 0.9rem;
}

/* Script Detail Sections */
.script-detail-section {
    margin-bottom: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
}

.script-detail-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.script-detail-header:hover {
    background: #e9ecef;
}

.script-detail-title {
    font-weight: 600;
    color: #495057;
    flex: 1;
}

.script-toggle {
    font-size: 0.9rem;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.script-detail-content {
    padding: 1rem;
    max-height: 500px;
    overflow-y: auto;
}

.script-detail-content.collapsed {
    display: none;
}

/* Business Object Schema Styles */
.schema-summary {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.schema-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.schema-stat {
    background: white;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.9rem;
    border: 1px solid #e3f2fd;
}

.properties-container {
    margin-top: 1rem;
}

.properties-container h4 {
    color: #495057;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.property-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.property-item:hover {
    border-color: #adb5bd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.property-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-bottom: 0.5rem;
}

.property-name {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.property-type {
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.property-type.system-type {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.property-type.custom-type {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.required-badge {
    background: #dc3545;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
}

.array-badge {
    background: #6f42c1;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
}

.default-badge {
    background: #17a2b8;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
}

.property-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.3rem;
    font-style: italic;
}

.property-namespace {
    font-size: 0.8rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    margin-top: 0.3rem;
    font-family: 'Courier New', monospace;
}

.property-reference {
    font-size: 0.8rem;
    color: #495057;
    background: #e9ecef;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    margin-top: 0.3rem;
    font-family: 'Courier New', monospace;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
    margin-bottom: 1rem;
}

.no-data {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 1rem 0;
}

/* Business Object Type Indicator */
.business-object-indicator {
    background: #e3f2fd;
    color: #1565c0;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

/* Cross-Reference Resolution Styles */
.resolved-type-container {
    margin-top: 0.5rem;
    border-left: 3px solid #007bff;
    padding-left: 0.75rem;
    background: #f8f9fa;
    border-radius: 0 4px 4px 0;
}

.resolved-type-header {
    margin-bottom: 0.5rem;
}

.resolved-type-toggle {
    cursor: pointer;
    color: #007bff;
    font-weight: 500;
    user-select: none;
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.resolved-type-toggle:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.resolved-type-content {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
}

/* Badge Styles for Status Indicators */
.circular-badge {
    background: #ffc107;
    color: #212529;
    padding: 0.15rem 0.4rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.unresolved-badge {
    background: #dc3545;
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

/* Note Styles */
.circular-reference-note {
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

.unresolved-reference-note {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

/* Business Object Summary Styles */
.business-object-summary {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.schema-stats {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.property-count {
    font-weight: 500;
    color: #495057;
}

.schema-indicator {
    padding: 0.15rem 0.4rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: default;
}

.schema-indicator.custom-types {
    background: #e3f2fd;
    color: #1565c0;
}

.schema-indicator.cross-refs {
    background: #f3e5f5;
    color: #7b1fa2;
}

.schema-indicator.circular-refs {
    background: #fff3e0;
    color: #ef6c00;
}

.schema-namespace {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Circular Reference Styles */
.circular-ref-label {
    background: #fff3e0;
    color: #ef6c00;
    padding: 0.15rem 0.4rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.circular-reference-info {
    background: #fff8e1;
    border: 1px solid #ffcc02;
    border-radius: 4px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.circular-reference-info p {
    margin: 0.25rem 0;
}

.circular-reference-info .circular-note {
    font-style: italic;
    color: #ef6c00;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #ffcc02;
}

/* Enhanced Property Item Nesting */
.property-item[style*="margin-left"] {
    border-left: 2px solid #e9ecef;
    position: relative;
}

.property-item[style*="margin-left"]::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff 0%, #6c757d 100%);
    opacity: 0.3;
}

/* Improved readability for nested structures */
.property-item[style*="margin-left: 20px"] .property-header {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 0.5rem;
}

.property-item[style*="margin-left: 40px"] .property-header {
    background-color: #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
}

.property-item[style*="margin-left: 60px"] .property-header {
    background-color: #dee2e6;
    border-radius: 4px;
    padding: 0.5rem;
}

/* 🆕 Toolkit Object Indicators */
.toolkit-indicator {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    margin-left: 8px;
    font-weight: bold;
}

.app-indicator {
    background: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    margin-left: 8px;
    font-weight: bold;
}

.object-count-breakdown {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
}

.app-count {
    background: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.85em;
    font-weight: bold;
}

.toolkit-count {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.85em;
    font-weight: bold;
}

.total-count {
    font-weight: bold;
    margin-left: 4px;
    color: #333;
}

/* Statistics enhancement */
.statistics-section {
    margin-bottom: 20px;
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.stat-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    text-align: center;
}

.stat-value {
    font-size: 1.5em;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 5px;
}

.stat-card.app-objects .stat-value {
    color: #28a745;
}

.stat-card.toolkit-objects .stat-value {
    color: #007bff;
}

.stat-card.total-objects .stat-value {
    color: #343a40;
}

.stat-card.toolkits .stat-value {
    color: #6f42c1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .header {
        padding: 1rem;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .object-types-list {
        grid-template-columns: 1fr;
    }
    
    .search-controls {
        flex-direction: column;
    }
    
    #search-button, #clear-search-button {
        width: 100%;
    }
    
    .schema-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .property-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .property-type {
        align-self: flex-start;
    }
}

/* Minimal Object Item Styles */
.object-header-minimal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.object-name-minimal {
    font-weight: 600;
    font-size: 0.95rem;
    color: #2c3e50;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.object-details-minimal {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.detail-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.detail-badge.scripts {
    background-color: #e3f2fd;
    color: #1976d2;
}

.detail-badge.variables {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.detail-badge.elements {
    background-color: #e8f5e8;
    color: #388e3c;
}

.detail-badge.properties {
    background-color: #fff3e0;
    color: #f57c00;
}

.source-badge {
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.source-badge.app {
    background-color: #e3f2fd;
    color: #1976d2;
}

.source-badge.toolkit {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

/* Updated Object Items for Minimal Design */
.object-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.object-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    transform: translateY(-1px);
}

.object-item.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}/* 
Search Result Styles */
.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.search-result-badges {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-source-badge {
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.search-source-badge.app {
    background-color: #e3f2fd;
    color: #1976d2;
}

.search-source-badge.toolkit {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.search-result-type {
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 500;
}

.search-result-preview {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-top: 0.5rem;
}

.highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 600;
}

/* Object Type Cards - Enhanced for Toolkit Toggle */
.object-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.object-count-breakdown {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.app-count, .toolkit-count {
    font-size: 0.8rem;
    padding: 0.1rem 0.3rem;
    border-radius: 10px;
    font-weight: 500;
}

.app-count {
    background-color: #e3f2fd;
    color: #1976d2;
}

.toolkit-count {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.total-count {
    font-weight: 700;
    font-size: 1.1rem;
    color: #2c3e50;
}
/*
 Business Object Builder Styles */
.business-object-builder {
    margin-bottom: 2rem;
}

.builder-description {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #1565c0;
}

.builder-description p {
    margin: 0;
    font-size: 0.95rem;
}

/* JSON Input Section */
.json-input-section {
    margin-bottom: 1.5rem;
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.input-header label {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.input-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.example-btn, .clear-btn, .validate-btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.example-btn {
    background: #17a2b8;
    color: white;
}

.example-btn:hover {
    background: #138496;
}

.clear-btn {
    background: #6c757d;
    color: white;
}

.clear-btn:hover {
    background: #545b62;
}

.validate-btn {
    background: #28a745;
    color: white;
}

.validate-btn:hover {
    background: #218838;
}

.json-textarea {
    width: 100%;
    min-height: 300px;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    background: #f8f9fa;
    transition: border-color 0.2s ease;
}

.json-textarea:focus {
    outline: none;
    border-color: #007bff;
    background: white;
}

.json-textarea.valid {
    border-color: #28a745;
    background: #f8fff9;
}

.json-textarea.invalid {
    border-color: #dc3545;
    background: #fff8f8;
}

.json-validation {
    margin-top: 0.5rem;
    padding: 0.75rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.json-validation.valid {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.json-validation.invalid {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* TWX File Selection */
.twx-selection-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.selection-header label {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    display: block;
}

.file-selection-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.select-twx-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.select-twx-btn:hover {
    background: #0056b3;
}

.selected-file-info {
    color: #28a745;
    font-weight: 500;
    font-size: 0.9rem;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.clear-file-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background-color 0.2s ease;
}

.clear-file-btn:hover {
    background: #c82333;
}

/* Generation Options */
.generation-options {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.options-header label {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    display: block;
}

.options-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.option-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.95rem;
    color: #495057;
}

.option-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #dee2e6;
    border-radius: 3px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.2s ease;
}

.option-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #007bff;
    border-color: #007bff;
}

.option-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 14px;
    font-weight: bold;
}

.option-checkbox:hover .checkmark {
    border-color: #007bff;
}

/* Generation Controls */
.generation-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.generate-btn, .preview-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.generate-btn {
    background: #28a745;
    color: white;
    flex: 1;
    min-width: 200px;
}

.generate-btn:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-1px);
}

.generate-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.preview-btn {
    background: #17a2b8;
    color: white;
    min-width: 150px;
}

.preview-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* Generation Results */
.generation-results {
    margin-top: 1.5rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.results-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 6px 6px 0 0;
}

.results-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1rem;
}

.results-content {
    padding: 1.5rem;
}

.result-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.result-stat {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.result-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.result-stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.result-objects {
    margin-top: 1rem;
}

.result-object {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.75rem;
}

.result-object-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.result-object-name {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.result-object-id {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
    background: #e9ecef;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
}

.result-object-details {
    font-size: 0.9rem;
    color: #6c757d;
}

.result-errors, .result-warnings {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 4px;
}

.result-errors {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.result-warnings {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.result-errors h5, .result-warnings h5 {
    margin: 0 0 0.5rem 0;
    font-size: 0.95rem;
}

.result-errors ul, .result-warnings ul {
    margin: 0;
    padding-left: 1.5rem;
}

.result-errors li, .result-warnings li {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

/* Type Reference */
.type-reference {
    margin-top: 1.5rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.reference-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.reference-header:hover {
    background: #e9ecef;
}

.reference-header span:first-child {
    font-weight: 600;
    color: #495057;
}

.reference-toggle {
    color: #6c757d;
    transition: transform 0.3s ease;
}

.reference-toggle.expanded {
    transform: rotate(90deg);
}

.reference-content {
    padding: 1.5rem;
}

.type-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.type-category h5 {
    margin: 0 0 0.75rem 0;
    color: #495057;
    font-size: 1rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.25rem;
}

.type-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.type-item {
    background: #e3f2fd;
    color: #1565c0;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid #bbdefb;
    transition: all 0.2s ease;
}

.type-item:hover {
    background: #bbdefb;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .input-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .input-controls {
        width: 100%;
        justify-content: flex-start;
    }
    
    .file-selection-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .generation-controls {
        flex-direction: column;
    }
    
    .generate-btn, .preview-btn {
        width: 100%;
        min-width: auto;
    }
    
    .result-summary {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .type-categories {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .result-summary {
        grid-template-columns: 1fr;
    }
    
    .result-object-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* AI Configuration Modal Styles */
.ai-config-modal {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.config-section {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fafafa;
}

.config-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 14px;
    box-sizing: border-box;
}

.setting-group textarea {
    height: 80px;
    font-family: monospace;
    resize: vertical;
}

.setting-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

#connection-status {
    margin-top: 10px;
    padding: 10px;
    border-radius: 3px;
}

/* AI Analysis Results Styles */
.ai-analysis-section {
    margin-top: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fafafa;
}

.ai-analysis-header {
    background-color: #f0f0f0;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.ai-analysis-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.analysis-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.severity-critical { color: #dc3545; }
.severity-warning { color: #ffc107; }
.severity-info { color: #17a2b8; }

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.analysis-table th,
.analysis-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
}

/* Specific styling for code context column to prevent overflow */
.analysis-table td:nth-child(8) {
    max-width: 300px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow: hidden;
}

.analysis-table td:nth-child(8) .code-context-enhanced,
.analysis-table td:nth-child(8) .code-context-simple {
    max-width: 100%;
    overflow-x: auto;
    white-space: nowrap;
}

.analysis-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.analysis-table tr:hover {
    background-color: #f5f5f5;
}

.issue-severity {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.issue-severity.critical {
    background-color: #dc3545;
    color: white;
}

.issue-severity.warning {
    background-color: #ffc107;
    color: #212529;
}

.issue-severity.info {
    background-color: #17a2b8;
    color: white;
}

.issue-description {
    max-width: 300px;
    word-wrap: break-word;
}

.issue-suggestion {
    max-width: 250px;
    word-wrap: break-word;
    font-style: italic;
    color: #666;
}

.overall-score {
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 3px;
}

.score-A { background-color: #28a745; color: white; }
.score-B { background-color: #6f42c1; color: white; }
.score-C { background-color: #ffc107; color: #212529; }
.score-D { background-color: #fd7e14; color: white; }
.score-F { background-color: #dc3545; color: white; }

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: #28a745;
}

.notification.error {
    background-color: #dc3545;
}

.notification.info {
    background-color: #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* AI Analysis Button Styles */
.ai-analysis-controls-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.ai-analysis-controls-section h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.ai-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.scripts-header {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
}

.ai-analyze-btn {
    background-color: #6f42c1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.ai-analyze-btn:hover:not(:disabled) {
    background-color: #5a32a3;
}

.ai-analyze-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
}

.btn-primary:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #545b62;
}

.btn-secondary:disabled {
    background-color: #adb5bd;
    cursor: not-allowed;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.btn-danger:hover:not(:disabled) {
    background-color: #c82333;
}

.btn-danger:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Progress Bar Styles */
.progress-container {
    margin: 15px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #6f42c1);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* Object Type Filters */
.object-type-filters {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.object-type-filters h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.filter-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.filter-checkbox {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    cursor: pointer;
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
    transition: background-color 0.2s ease;
    min-width: 150px;
}

.filter-checkbox:hover {
    background-color: #f8f9fa;
}

.filter-checkbox input[type="checkbox"] {
    margin-right: 8px;
}

.filter-checkbox span {
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
}

.filter-checkbox small {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.2;
}

.filter-options {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #dee2e6;
}

.progress-info {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 14px;
    font-weight: 500;
}

.results-count {
    color: #495057;
    font-weight: 500;
}

/* Rate Limit Warning */
.rate-limit-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.4;
}

.rate-limit-warning strong {
    display: block;
    margin-bottom: 5px;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0;
    margin: 0;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.tab-button:hover {
    background-color: #e9ecef;
    color: #495057;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: white;
}

/* Tab Content */
.tab-content {
    display: none;
    flex: 1;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* AI Analysis Tab Styles */
.ai-analysis-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
}

.ai-analysis-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    overflow: hidden;
}

.ai-config-panel {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    flex-shrink: 0;
}

.ai-config-panel h3 {
    margin: 0 0 20px 0;
    color: #495057;
}

.ai-config-panel h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.ai-results-panel {
    flex: 1;
    overflow: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
}

.ai-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

/* Results Filter Controls */
.results-filter-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 500;
    color: #495057;
}

.filter-group select,
.filter-group input {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

.filter-group input {
    min-width: 150px;
}

.filter-group button {
    margin-top: 18px; /* Align with other controls */
}

/* Code Line Display */
.code-line {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 3px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #495057;
    border: 1px solid #e9ecef;
}

/* Prompt Editor Modal */
.large-modal {
    max-width: 900px;
    width: 90%;
}

.prompt-editor-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.prompt-info {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 5px;
    padding: 15px;
}

.prompt-info p {
    margin: 0 0 10px 0;
    color: #0056b3;
}

.prompt-variables {
    font-size: 14px;
    color: #495057;
}

.prompt-variables code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
}

.prompt-editor label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: #495057;
}

.prompt-editor textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    min-height: 400px;
}

.prompt-editor textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.prompt-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

/* Static Review Tab Styles */
.static-review-section {
    padding: 20px;
}

.static-review-container {
    max-width: 1200px;
    margin: 0 auto;
}

.static-config-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.config-group h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.filter-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-checkboxes label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
}

.filter-checkboxes input[type="checkbox"] {
    margin: 0;
}

/* Enhanced checkbox styling */
.checkbox-icon {
    margin-right: 5px;
    font-size: 14px;
}

.option-description {
    display: block;
    margin-left: 25px;
    margin-top: 2px;
    color: #6c757d;
    font-size: 12px;
    font-style: italic;
}

.object-count-display {
    margin-top: 10px;
    padding: 8px;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    text-align: center;
}

.object-count-display small {
    color: #0056b3;
    font-weight: 500;
}

/* Severity badge styling in config */
.severity-badge {
    display: inline-block;
    margin-right: 5px;
    font-size: 10px;
    min-width: 50px;
    text-align: center;
}

.static-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
}

.primary-action {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.secondary-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.static-controls button {
    white-space: nowrap;
}

.btn-large {
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    min-width: 200px;
}

.btn-large:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Static Analysis Progress */
.progress-section {
    margin: 20px 0;
    padding: 15px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
}

/* Static Results Styling */
.results-section {
    margin-top: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

.results-summary {
    font-size: 14px;
    color: #6c757d;
}

/* Results Filters */
.results-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

.filter-group select,
.filter-group input {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    min-width: 120px;
}

.filter-group input {
    min-width: 200px;
}

/* Enhanced Results Table */
.enhanced-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 13px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    table-layout: fixed; /* Enable fixed layout for resizable columns */
}

.enhanced-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
    position: relative;
    overflow: hidden;
}

/* Resizable column handle */
.enhanced-table th::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    cursor: col-resize;
    z-index: 11;
}

.enhanced-table th:hover::after {
    background: rgba(0, 123, 255, 0.3);
}

.enhanced-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.enhanced-table th.sortable:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.sort-indicator {
    margin-left: 5px;
    opacity: 0.5;
}

.sort-indicator::after {
    content: '↕';
}

.sort-indicator.asc::after {
    content: '↑';
    opacity: 1;
}

.sort-indicator.desc::after {
    content: '↓';
    opacity: 1;
}

.enhanced-table td {
    border: 1px solid #dee2e6;
    padding: 10px 8px;
    vertical-align: top;
    line-height: 1.4;
    overflow: visible;
}

.enhanced-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.enhanced-table tbody tr:hover {
    background-color: #e3f2fd;
    transition: background-color 0.2s ease;
}

.enhanced-table tbody tr.filtered-out {
    display: none;
}

/* Column-specific styling */
.code-column {
    width: 450px;
    max-width: 600px;
    min-width: 400px;
    overflow: visible !important;
}

.hierarchical-column {
    width: 200px;
    min-width: 180px;
}

.actions-column {
    width: 100px;
    text-align: center;
}

/* Ensure code context cells don't truncate */
.enhanced-table td.code-column,
.enhanced-table .code-column {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
}

/* Hierarchical Classification Styling */
.hierarchical-classification {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px 0;
}

.classification-level-1,
.classification-level-2,
.classification-level-3 {
    display: flex;
    align-items: center;
}

.classification-level-2 {
    padding-left: 12px;
    position: relative;
}

.classification-level-2::before {
    content: '└─';
    position: absolute;
    left: 0;
    color: #adb5bd;
    font-size: 10px;
}

.classification-level-3 {
    padding-left: 24px;
    position: relative;
}

.classification-level-3::before {
    content: '└─';
    position: absolute;
    left: 12px;
    color: #adb5bd;
    font-size: 10px;
}

.rule-code {
    font-size: 11px;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
    color: #495057;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.hierarchical-cell {
    vertical-align: top;
    padding: 8px !important;
}

/* Action buttons */
.action-btn {
    display: inline-block;
    padding: 4px 8px;
    margin: 0 2px;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-btn.ignore {
    background-color: #6c757d;
    color: white;
}

.action-btn.ignore:hover {
    background-color: #5a6268;
}

.action-btn.done {
    background-color: #28a745;
    color: white;
}

.action-btn.done:hover {
    background-color: #218838;
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Severity badges for static analysis */
.severity-error {
    background-color: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.severity-warning {
    background-color: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.severity-info {
    background-color: #17a2b8;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

/* Category badges */
.category-syntax { background-color: #e74c3c; }
.category-security { background-color: #e67e22; }
.category-best_practice { background-color: #f39c12; }
.category-complexity { background-color: #9b59b6; }
.category-style { background-color: #3498db; }
.category-async { background-color: #1abc9c; }
.category-general { background-color: #95a5a6; }
.category-performance { background-color: #fd7e14; }
.category-unknown { background-color: #6c757d; }
.category-content-type,
.category-content_type { background-color: #17a2b8; }

.category-badge {
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

/* Enhanced Code Display */
.static-code-context {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 6px;
    font-size: 12px;
    border-left: 4px solid #007bff;
    white-space: pre;
    overflow-x: auto;
    overflow-y: visible;
    line-height: 1.6;
    color: #495057;
    border: 1px solid #e9ecef;
    max-width: 100%;
    width: 100%;
    display: block;
    max-height: 200px;
    transition: max-height 0.3s ease, background-color 0.2s ease;
    cursor: pointer;
    text-overflow: clip;
    word-wrap: normal;
}

.static-code-context:hover {
    background-color: #e9ecef;
}

.static-code-context.expanded {
    max-height: none;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Ensure all child elements don't truncate */
.static-code-context * {
    text-overflow: clip !important;
    overflow: visible !important;
    white-space: pre !important;
}

/* Legacy support for old class name */
.static-code-line {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;
    border-left: 4px solid #007bff;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.5;
    color: #495057;
    border: 1px solid #e9ecef;
    max-width: 100%;
    overflow: visible;
    display: block;
}

.static-code-line:hover {
    background-color: #e9ecef;
}

/* Code Context Display */
.code-context {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-left: 4px solid #007bff;
    overflow: visible;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-width: 100%;
}

/* Simple code context for clean plain text display */
.code-context-simple {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    border: 1px solid #dee2e6;
    color: #495057;
    white-space: pre;
    overflow: visible;
    max-width: 100%;
    display: block;
    margin: 0;
}

/* Enhanced code context with line highlighting */
.code-context-enhanced {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    color: #495057;
    max-width: 100%;
    width: 100%;
    display: block;
    margin: 0;
    overflow-x: auto;
    overflow-y: visible;
    padding: 0;
    text-overflow: clip;
}

.code-context-enhanced .code-line {
    padding: 3px 10px;
    white-space: pre;
    display: block;
    min-height: 20px;
    overflow: visible;
    text-overflow: clip;
}

.code-context-enhanced .code-line.error-line {
    background-color: #fff5f5;
    border-left: 3px solid #dc3545;
    font-weight: 500;
    padding-left: 7px;
}

.code-context-enhanced .line-number {
    color: #6c757d;
    margin-right: 12px;
    user-select: none;
    display: inline-block;
    min-width: 35px;
    text-align: right;
}

.code-context-enhanced .line-content {
    display: inline;
    white-space: pre;
    overflow: visible;
    text-overflow: clip;
    max-width: none;
    word-wrap: normal;
    color: #495057;
}

.code-context-enhanced .error-line .line-number {
    color: #dc3545;
    font-weight: 600;
}

.code-line {
    display: block;
    padding: 1px 0;
    margin: 0;
    border-left: 3px solid transparent;
    padding-left: 8px;
}

.code-line.current-line {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    font-weight: 600;
    margin: 0 -8px;
    padding-left: 16px;
    padding-right: 8px;
}

.line-number {
    display: inline-block;
    width: 40px;
    text-align: right;
    color: #6c757d;
    font-size: 11px;
    margin-right: 8px;
    user-select: none;
}

.current-line .line-number {
    color: #856404;
    font-weight: 600;
}

.line-content {
    display: inline;
    white-space: pre;
}

.current-line .line-content {
    color: #856404;
}

.code-context-empty {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Table column sizing for better layout */
.results-table .code-column {
    width: 450px;
    max-width: 600px;
    min-width: 400px;
}

.results-table .actions-column {
    width: 80px;
    max-width: 80px;
    min-width: 80px;
    text-align: center;
}

/* Syntax highlighting for common JavaScript patterns */
.line-content {
    /* Keywords */
    --keyword-color: #0000ff;
    --string-color: #008000;
    --comment-color: #808080;
    --number-color: #ff0000;
    --operator-color: #000000;
}

/* Simple syntax highlighting using CSS (basic patterns) */
.line-content:has-text("function") {
    color: var(--keyword-color);
}

.line-content:has-text("var "),
.line-content:has-text("let "),
.line-content:has-text("const ") {
    color: var(--keyword-color);
}

/* Category badges */
.category-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-security {
    background-color: #dc3545;
    color: white;
}

.category-logic {
    background-color: #fd7e14;
    color: white;
}

.category-runtime {
    background-color: #ffc107;
    color: #212529;
}

.category-best-practice,
.category-best_practice {
    background-color: #28a745;
    color: white;
}

.category-general {
    background-color: #6c757d;
    color: white;
}

.category-analysis-error,
.category-analysis_error {
    background-color: #e83e8c;
    color: white;
}

.category-performance {
    background-color: #fd7e14;
    color: white;
}

.category-unknown {
    background-color: #6c757d;
    color: white;
}

.category-content-type,
.category-content_type {
    background-color: #17a2b8;
    color: white;
}