{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "checkJs": true, "noEmit": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": true, "noImplicitThis": false, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "declaration": false, "declarationMap": false, "sourceMap": false, "outDir": "./dist", "removeComments": false, "importHelpers": false, "downlevelIteration": false, "experimentalDecorators": false, "emitDecoratorMetadata": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.js", "twx-viewer-new.js", "start-viewer.js"], "exclude": ["node_modules", "dist", "output", "**/*.min.js", "**/*.test.js", "**/*.spec.js"], "typeAcquisition": {"enable": true, "include": ["node", "dom"], "exclude": []}}